'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme } from '@/app/theme';
import SettingsLayout from '../components/SettingsLayout';
import { Plus, Search, Loader2, AlertCircle, Users, Trash, X, ChevronDown, Edit, Building } from 'lucide-react';

const AdminContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
  }

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.base};
    gap: ${settingsTheme.spacing.base};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
`;

const Title = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
`;

const FilterContainer = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  align-items: center;
  flex-wrap: wrap;
`;

const OrganizationSelect = styled.div`
  position: relative;
  min-width: 250px;
`;

const SelectButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.background.light};
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    border-color: ${settingsTheme.colors.primary};
  }

  &:focus {
    outline: none;
    border-color: ${settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${settingsTheme.colors.primary}20;
  }
`;

const SelectDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${settingsTheme.colors.background.main};
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  box-shadow: ${settingsTheme.shadows.lg};
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  margin-top: 4px;
`;

const SelectOption = styled.button`
  display: block;
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  text-align: left;
  background: none;
  border: none;
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }

  &:focus {
    outline: none;
    background-color: ${settingsTheme.colors.primary}10;
  }
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.primary};
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  padding-left: 40px;
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${settingsTheme.colors.primary}20;
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${settingsTheme.colors.text.tertiary};
  width: 16px;
  height: 16px;
`;

// Table components
const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: ${settingsTheme.shadows.sm};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
`;

const THead = styled.thead`
  background-color: ${settingsTheme.colors.background.light};
`;

const TH = styled.th`
  text-align: left;
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const TR = styled.tr`
  transition: ${settingsTheme.transitions.default};
  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const TD = styled.td`
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const TableActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.primary};
  }

  &.delete {
    color: ${settingsTheme.colors.error.main};
  }
`;

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const Avatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #4b5563;
  font-size: 0.875rem;
  background-size: cover;
  background-position: center;
  ${props =>
    props.$imageUrl &&
    `
    background-image: url(${props.$imageUrl});
    color: transparent;
  `}
`;

const UserCell = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.div`
  font-weight: 500;
  color: #111827;
`;

const UserEmail = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0f2fe;
  color: #0369a1;
`;

const ChatStatusBadge = styled.span<{ $status: 'admin' | 'participant' | 'inactive' }>`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: ${props => {
    switch (props.$status) {
      case 'admin':
        return '#dcfce7';
      case 'participant':
        return '#e0f2fe';
      case 'inactive':
        return '#f3f4f6';
      default:
        return '#f3f4f6';
    }
  }};
  color: ${props => {
    switch (props.$status) {
      case 'admin':
        return '#166534';
      case 'participant':
        return '#0369a1';
      case 'inactive':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  }};
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
`;

const ChatStatusIcon = styled.div<{ $status: 'admin' | 'participant' | 'inactive' }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${props => {
    switch (props.$status) {
      case 'admin':
        return '#16a34a';
      case 'participant':
        return '#0284c7';
      case 'inactive':
        return '#9ca3af';
      default:
        return '#9ca3af';
    }
  }};
`;

const NoResults = styled.div`
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${settingsTheme.spacing.xl};
`;

const ErrorContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error}10;
  border: 1px solid ${settingsTheme.colors.error}30;
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const SuccessContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.success}10;
  border: 1px solid ${settingsTheme.colors.success}30;
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.success};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xl};
  text-align: center;
  color: ${settingsTheme.colors.text.secondary};
`;

const EmptyIcon = styled(Users)`
  width: 64px;
  height: 64px;
  margin-bottom: ${settingsTheme.spacing.md};
  opacity: 0.5;
`;

const EmptyTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0 0 ${settingsTheme.spacing.sm} 0;
`;

const EmptyDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  margin: 0;
  max-width: 400px;
`;

// Modal Styles
const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${settingsTheme.spacing.md};
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const ModalTitle = styled.h2`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${settingsTheme.typography.fontSizes.xl};
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  justify-content: flex-end;
  margin-top: ${settingsTheme.spacing.lg};
`;

const ModalButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
  border: 1px solid
    ${props =>
      props.variant === 'primary' ? settingsTheme.colors.primary : settingsTheme.colors.border};
  background-color: ${props =>
    props.variant === 'primary'
      ? settingsTheme.colors.primary
      : settingsTheme.colors.background.light};
  color: ${props => (props.variant === 'primary' ? 'white' : settingsTheme.colors.text.primary)};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};

  &:hover {
    background-color: ${props =>
      props.variant === 'primary'
        ? settingsTheme.colors.primaryHover
        : settingsTheme.colors.background.lighter};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Form Styles
const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.md};
  margin: ${settingsTheme.spacing.md} 0;
`;

const FormRow = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xs};
  flex: 1;
`;

const FormLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
`;

const FormInput = styled.input.withConfig({
  shouldForwardProp: prop => prop !== 'hasError',
})<{ hasError?: boolean }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  border: 1px solid
    ${props => (props.hasError ? settingsTheme.colors.error : settingsTheme.colors.border)};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props =>
      props.hasError ? settingsTheme.colors.error : settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px
      ${props =>
        props.hasError ? settingsTheme.colors.error + '20' : settingsTheme.colors.primary + '20'};
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const FormError = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  color: ${settingsTheme.colors.error};
  margin-top: ${settingsTheme.spacing.xs};
`;

const RequiredIndicator = styled.span`
  color: ${settingsTheme.colors.error};
  margin-left: 2px;
`;

const OrganizationCheckboxContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  padding: ${settingsTheme.spacing.md};
`;

const CheckboxItem = styled.label`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: ${settingsTheme.colors.primary};
`;

const CheckboxLabel = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
`;

const OrganizationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xs};
`;

const OrganizationBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0f2fe;
  color: #0369a1;
  white-space: nowrap;
`;



interface Organization {
  id: number;
  name: string;
  description?: string;
  assignedAt?: string; // For admin organizations
}

interface Admin {
  id: number;
  userId: number;
  organizationId: number;
  assignedAt: string;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRole: {
      id: number;
      name: string;
    };
  };
  assignedByUser?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  organization: {
    id: number;
    name: string;
  };
  chatStatus?: {
    isParticipant: boolean;
    isAdmin: boolean;
    chatId?: number;
    departmentChats?: {
      total: number;
      participating: number;
    };
  };
  organizations?: Array<{
    id: number;
    name: string;
    assignedAt: string;
  }>;
}

export default function AdminPage() {
  const [mounted, setMounted] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingAdmins, setLoadingAdmins] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [addingAdmin, setAddingAdmin] = useState(false);
  const [editingAdmin, setEditingAdmin] = useState(false);
  const [selectedOrganizations, setSelectedOrganizations] = useState<number[]>([]);
  const [editingAdminData, setEditingAdminData] = useState<Admin | null>(null);
  const [showManageOrgsModal, setShowManageOrgsModal] = useState(false);
  const [managingAdmin, setManagingAdmin] = useState<Admin | null>(null);
  const [adminOrganizations, setAdminOrganizations] = useState<Organization[]>([]);
  const [loadingAdminOrgs, setLoadingAdminOrgs] = useState(false);
  const [updatingOrganizations, setUpdatingOrganizations] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Filter admins based on search term
  const filteredAdmins = admins.filter(
    admin =>
      admin.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const fetchOrganizations = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      // Get user's owned organizations using the permissions API
      const response = await fetch('/api/v1/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch organizations');
      }

      const data = await response.json();
      if (data) {
        // Filter only owned organizations
        setOrganizations(data.user.organizations || []);
        // Auto-select first organization if available
        if (data.user.organizations.length > 0) {
          setSelectedOrganization(data.user.organizations[0]);
        }
      }
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  const fetchAdmins = async (organizationId: number) => {
    setLoadingAdmins(true);
    setError(null);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?organizationId=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch admins');
      }

      const data = await response.json();
      const admins = data.admins || [];

      // Fetch chat status for each admin
      const adminsWithChatStatus = await Promise.all(
        admins.map(async (admin: Admin) => {
          try {
            // Fetch organization chat information
            const chatResponse = await fetch(`/api/v1/chat?organizationId=${organizationId}&chatType=organization`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            let orgChatStatus = {
              isParticipant: false,
              isAdmin: false,
              chatId: undefined as number | undefined,
            };

            if (chatResponse.ok) {
              const chatData = await chatResponse.json();
              const orgChat = chatData.chats?.[0];

              if (orgChat) {
                const chatUser = orgChat.chatUsers?.find((cu: any) => cu.userId === admin.userId);
                orgChatStatus = {
                  isParticipant: !!chatUser,
                  isAdmin: chatUser?.isAdmin || false,
                  chatId: orgChat.id,
                };
              }
            }

            // Fetch department chat information
            let departmentChatStatus = {
              total: 0,
              participating: 0,
            };

            try {
              const deptChatResponse = await fetch(`/api/v1/chat?organizationId=${organizationId}&chatType=department`, {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });

              if (deptChatResponse.ok) {
                const deptChatData = await deptChatResponse.json();
                const departmentChats = deptChatData.chats || [];

                departmentChatStatus.total = departmentChats.length;
                departmentChatStatus.participating = departmentChats.filter((chat: any) =>
                  chat.chatUsers?.some((cu: any) => cu.userId === admin.userId && cu.isAdmin)
                ).length;
              }
            } catch (deptChatError) {
              console.warn('Error fetching department chat status for admin:', admin.userId, deptChatError);
            }

            admin.chatStatus = {
              ...orgChatStatus,
              departmentChats: departmentChatStatus,
            };
          } catch (chatError) {
            console.warn('Error fetching chat status for admin:', admin.userId, chatError);
            admin.chatStatus = {
              isParticipant: false,
              isAdmin: false,
              departmentChats: {
                total: 0,
                participating: 0,
              },
            };
          }
          return admin;
        })
      );

      setAdmins(adminsWithChatStatus);
    } catch (err) {
      console.error('Error fetching admins:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch admins');
    } finally {
      setLoadingAdmins(false);
    }
  };



  const handleRemoveAdmin = async (adminId: number) => {
    if (!confirm('Are you sure you want to remove this admin?')) {
      return;
    }

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?id=${adminId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove admin');
      }

      setSuccessMessage('Admin removed successfully and removed from organization chat');
      setTimeout(() => setSuccessMessage(null), 5000);

      // Refresh the admins list
      if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }
    } catch (err) {
      console.error('Error removing admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove admin');
      setTimeout(() => setError(null), 5000);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const handleRepairChats = async () => {
    if (!selectedOrganization) {
      setError('Please select an organization first');
      return;
    }

    if (!confirm(`Are you sure you want to repair chat memberships for ${selectedOrganization.name}? This will ensure all admins are properly added to the organization chat.`)) {
      return;
    }

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/v1/admin-chat-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          action: 'repair',
          organizationId: selectedOrganization.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to repair chat memberships');
      }

      const result = await response.json();

      if (result.success) {
        setSuccessMessage(`Chat repair completed successfully! ${result.message}`);
      } else {
        setError(`Chat repair completed with issues: ${result.message}`);
      }

      // Refresh admins list to show updated chat status
      fetchAdmins(selectedOrganization.id);

      // Clear messages after 5 seconds
      setTimeout(() => {
        setError(null);
        setSuccessMessage(null);
      }, 5000);
    } catch (err) {
      console.error('Error repairing chats:', err);
      setError(err instanceof Error ? err.message : 'Failed to repair chat memberships');
      setTimeout(() => setError(null), 5000);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Fetch organizations on mount
  useEffect(() => {
    fetchOrganizations();
  }, []);

  // Fetch admins when selected organization changes
  useEffect(() => {
    if (selectedOrganization) {
      fetchAdmins(selectedOrganization.id);
    }
  }, [selectedOrganization]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    if (selectedOrganizations.length === 0) {
      errors.organizations = 'Please select at least one organization';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const isFormValid = () => {
    return (
      formData.firstName.trim() &&
      formData.lastName.trim() &&
      formData.email.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.password &&
      formData.password.length >= 6 &&
      formData.password === formData.confirmPassword &&
      selectedOrganizations.length > 0
    );
  };

  const handleOpenAddModal = () => {
    setShowAddModal(true);
    // Reset form data
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    // Pre-select current organization if available
    if (selectedOrganization) {
      setSelectedOrganizations([selectedOrganization.id]);
    } else {
      setSelectedOrganizations([]);
    }
  };

  const handleCloseAddModal = () => {
    setShowAddModal(false);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    setSelectedOrganizations([]);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleOrganizationToggle = (organizationId: number) => {
    setSelectedOrganizations(prev => {
      if (prev.includes(organizationId)) {
        return prev.filter(id => id !== organizationId);
      } else {
        return [...prev, organizationId];
      }
    });

    // Clear organizations error when user selects at least one
    if (formErrors.organizations) {
      setFormErrors(prev => ({
        ...prev,
        organizations: '',
      }));
    }
  };

  const handleAddAdmin = async () => {
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setAddingAdmin(true);

    try {
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please login again.');
        alert('You need to login first. Redirecting to login page...');
        window.location.href = '/login';
        return;
      }

      const requestBody = {
        organizationIds: selectedOrganizations,
        createUser: {
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          email: formData.email.trim(),
          phone: formData.phone.trim() || null,
          password: formData.password,
        },
      };

      const response = await fetch('/api/v1/organization-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to create admin');
      }

      const responseData = await response.json();

      // Show success message
      if (responseData.errors && responseData.errors.length > 0) {
        const chatErrors = responseData.errors.filter((err: string) => err.includes('Chat:'));
        const otherErrors = responseData.errors.filter((err: string) => !err.includes('Chat:'));

        if (otherErrors.length > 0) {
          setError(`Admin created with issues: ${otherErrors.join(', ')}`);
        } else {
          setSuccessMessage(`Admin created successfully! ${chatErrors.length > 0 ? 'Note: Some chat operations had warnings.' : 'Admin has been added to organization chats.'}`);
        }

        if (chatErrors.length > 0) {
          console.warn('Chat synchronization warnings:', chatErrors);
        }
      } else {
        setError(null);
        setSuccessMessage('Admin created successfully and added to organization chats!');
      }

      // Clear messages after 5 seconds
      setTimeout(() => {
        setError(null);
        setSuccessMessage(null);
      }, 5000);

      // Reset modal state
      handleCloseAddModal();

      // Refresh admins list
      if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }
    } catch (err) {
      console.error('Error creating admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to create admin');
    } finally {
      setAddingAdmin(false);
    }
  };

  const handleEditAdmin = (admin: Admin) => {
    setEditingAdminData(admin);
    setShowEditModal(true);
    // Pre-fill form with admin data
    setFormData({
      firstName: admin.user.firstName,
      lastName: admin.user.lastName,
      email: admin.user.email,
      phone: '', // Phone is not available in current admin data
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    // Set selected organizations (for now just the current organization)
    setSelectedOrganizations([admin.organizationId]);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingAdminData(null);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    setSelectedOrganizations([]);
  };

  const handleUpdateAdmin = async () => {
    if (!editingAdminData) return;

    // For edit, we don't require password fields
    const editErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      editErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      editErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      editErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      editErrors.email = 'Please enter a valid email address';
    }

    // Only validate password if it's provided
    if (formData.password && formData.password.length < 6) {
      editErrors.password = 'Password must be at least 6 characters';
    }

    if (formData.password && formData.password !== formData.confirmPassword) {
      editErrors.confirmPassword = 'Passwords do not match';
    }

    setFormErrors(editErrors);
    if (Object.keys(editErrors).length > 0) {
      return;
    }

    setEditingAdmin(true);

    try {
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please login again.');
        return;
      }

      // Update user information
      const updateUserBody: any = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
      };

      if (formData.phone.trim()) {
        updateUserBody.phone = formData.phone.trim();
      }

      if (formData.password) {
        updateUserBody.password = formData.password;
      }

      const response = await fetch(`/api/v1/user/${editingAdminData.user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updateUserBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update admin');
      }

      // Reset modal state
      handleCloseEditModal();

      // Refresh admins list
      if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }

      setError(null);
    } catch (err) {
      console.error('Error updating admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to update admin');
    } finally {
      setEditingAdmin(false);
    }
  };

  const handleManageOrganizations = async (admin: Admin) => {
    setManagingAdmin(admin);
    setShowManageOrgsModal(true);
    setLoadingAdminOrgs(true);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/admin-organizations?adminUserId=${admin.user.id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch admin organizations');
      }

      const data = await response.json();
      setAdminOrganizations(data.organizations || []);

      // Set selected organizations based on current assignments
      const currentOrgIds = data.organizations.map((org: any) => org.id);
      setSelectedOrganizations(currentOrgIds);
    } catch (err) {
      console.error('Error fetching admin organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch admin organizations');
    } finally {
      setLoadingAdminOrgs(false);
    }
  };

  const handleCloseManageOrgsModal = () => {
    setShowManageOrgsModal(false);
    setManagingAdmin(null);
    setAdminOrganizations([]);
    setSelectedOrganizations([]);
    setLoadingAdminOrgs(false);
  };

  const handleUpdateAdminOrganizations = async () => {
    if (!managingAdmin) return;

    setUpdatingOrganizations(true);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      // Get current organization IDs
      const currentOrgIds = adminOrganizations.map(org => org.id);

      // Find organizations to add and remove
      const toAdd = selectedOrganizations.filter(id => !currentOrgIds.includes(id));
      const toRemove = currentOrgIds.filter(id => !selectedOrganizations.includes(id));

      // Add new organizations
      if (toAdd.length > 0) {
        const addResponse = await fetch('/api/v1/admin-organizations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            adminUserId: managingAdmin.user.id,
            organizationIds: toAdd,
          }),
        });

        if (!addResponse.ok) {
          const errorData = await addResponse.json();
          throw new Error(errorData.error || 'Failed to add organizations');
        }
      }

      // Remove organizations
      if (toRemove.length > 0) {
        const removeResponse = await fetch('/api/v1/admin-organizations', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            adminUserId: managingAdmin.user.id,
            organizationIds: toRemove,
          }),
        });

        if (!removeResponse.ok) {
          const errorData = await removeResponse.json();
          throw new Error(errorData.error || 'Failed to remove organizations');
        }
      }

      // Close modal and refresh
      handleCloseManageOrgsModal();

      // Refresh admins list
      if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }

      setError(null);
    } catch (err) {
      console.error('Error updating admin organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to update admin organizations');
    } finally {
      setUpdatingOrganizations(false);
    }
  };

  // Prevent hydration errors
  if (!mounted) {
    return (
      <SettingsLayout>
        <AdminContainer>
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        </AdminContainer>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout>
      <AdminContainer>
        <Header>
          <HeaderLeft>
            <Title>
              <Users size={24} />
              Organization Admins
            </Title>
          </HeaderLeft>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <AddButton
              onClick={handleRepairChats}
              disabled={loading || !selectedOrganization}
              style={{ backgroundColor: '#f59e0b', borderColor: '#f59e0b' }}
            >
              <AlertCircle size={16} />
              Repair Chats
            </AddButton>
            <AddButton onClick={handleOpenAddModal} disabled={loading || organizations.length === 0}>
              <Plus size={16} />
              Add Admin
            </AddButton>
          </div>
        </Header>

        {error && (
          <ErrorContainer>
            <AlertCircle size={16} />
            {error}
          </ErrorContainer>
        )}

        {successMessage && (
          <SuccessContainer>
            <AlertCircle size={16} />
            {successMessage}
          </SuccessContainer>
        )}

        <FilterContainer>
          <OrganizationSelect>
            <SelectButton
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              disabled={loading || organizations.length === 0}
            >
              <span>
                {selectedOrganization ? selectedOrganization.name : 'Select Organization'}
              </span>
              <ChevronDown size={16} />
            </SelectButton>
            <SelectDropdown $isOpen={isDropdownOpen}>
              {organizations.map(org => (
                <SelectOption
                  key={org.id}
                  onClick={() => {
                    setSelectedOrganization(org);
                    setIsDropdownOpen(false);
                  }}
                >
                  {org.name}
                </SelectOption>
              ))}
            </SelectDropdown>
          </OrganizationSelect>

          <SearchContainer>
            <SearchIcon />
            <SearchInput
              type="text"
              placeholder="Search admins by name or email..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
        </FilterContainer>

        {loading ? (
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        ) : !selectedOrganization ? (
          <EmptyState>
            <EmptyIcon />
            <EmptyTitle>No Organization Selected</EmptyTitle>
            <EmptyDescription>
              Please select an organization from the dropdown above to view and manage admins.
            </EmptyDescription>
          </EmptyState>
        ) : loadingAdmins ? (
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        ) : filteredAdmins.length > 0 ? (
          <Table>
            <THead>
              <tr>
                <TH>Admin</TH>
                <TH>Role</TH>
                <TH>Chat Status</TH>
                <TH>Assigned Date</TH>
                <TH>Assigned By</TH>
                <TH>Actions</TH>
              </tr>
            </THead>
            <tbody>
              {filteredAdmins.map(admin => (
                <TR key={admin.id}>
                  <TD>
                    <UserCell>
                      <Avatar $imageUrl={admin.user.imageUrl}>
                        {getInitials(admin.user.firstName, admin.user.lastName)}
                      </Avatar>
                      <UserInfo>
                        <UserName>
                          {admin.user.firstName} {admin.user.lastName}
                        </UserName>
                        <UserEmail>{admin.user.email}</UserEmail>
                      </UserInfo>
                    </UserCell>
                  </TD>
                  <TD>
                    <Badge>Admin</Badge>
                  </TD>
                  <TD>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                      {admin.chatStatus ? (
                        <>
                          {admin.chatStatus.isAdmin ? (
                            <ChatStatusBadge $status="admin">
                              <ChatStatusIcon $status="admin" />
                              Org Admin
                            </ChatStatusBadge>
                          ) : admin.chatStatus.isParticipant ? (
                            <ChatStatusBadge $status="participant">
                              <ChatStatusIcon $status="participant" />
                              Org Member
                            </ChatStatusBadge>
                          ) : (
                            <ChatStatusBadge $status="inactive">
                              <ChatStatusIcon $status="inactive" />
                              Not in Org Chat
                            </ChatStatusBadge>
                          )}

                          {admin.chatStatus.departmentChats && (
                            <ChatStatusBadge $status={admin.chatStatus.departmentChats.participating > 0 ? "admin" : "inactive"}>
                              <ChatStatusIcon $status={admin.chatStatus.departmentChats.participating > 0 ? "admin" : "inactive"} />
                              Dept: {admin.chatStatus.departmentChats.participating}/{admin.chatStatus.departmentChats.total}
                            </ChatStatusBadge>
                          )}
                        </>
                      ) : (
                        <ChatStatusBadge $status="inactive">
                          <ChatStatusIcon $status="inactive" />
                          Unknown
                        </ChatStatusBadge>
                      )}
                    </div>
                  </TD>
                  <TD>{formatDate(admin.assignedAt)}</TD>
                  <TD>
                    {admin.assignedByUser
                      ? `${admin.assignedByUser.firstName} ${admin.assignedByUser.lastName}`
                      : 'System'}
                  </TD>
                  <TD>
                    <ActionButtonContainer>
                      <TableActionButton
                        onClick={() => handleEditAdmin(admin)}
                        title="Edit Admin"
                      >
                        <Edit size={16} />
                      </TableActionButton>
                      <TableActionButton
                        onClick={() => handleManageOrganizations(admin)}
                        title="Manage Organizations"
                      >
                        <Building size={16} />
                      </TableActionButton>
                      <TableActionButton
                        className="delete"
                        onClick={() => handleRemoveAdmin(admin.id)}
                        title="Remove Admin"
                      >
                        <Trash size={16} />
                      </TableActionButton>
                    </ActionButtonContainer>
                  </TD>
                </TR>
              ))}
            </tbody>
          </Table>
        ) : (
          <Table>
            <THead>
              <tr>
                <TH>Admin</TH>
                <TH>Organizations</TH>
                <TH>Role</TH>
                <TH>Assigned Date</TH>
                <TH>Assigned By</TH>
                <TH>Actions</TH>
              </tr>
            </THead>
            <tbody>
              <TR>
                <TD colSpan={6}>
                  <NoResults>
                    {searchTerm
                      ? `No admins match your search "${searchTerm}". Try a different search term.`
                      : `${selectedOrganization?.name} doesn't have any admins yet. Click "Add Admin" to assign your first admin.`}
                  </NoResults>
                </TD>
              </TR>
            </tbody>
          </Table>
        )}
      </AdminContainer>

      {/* Create Admin Modal */}
      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Create New Admin</ModalTitle>
              <CloseButton onClick={handleCloseAddModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <FormContainer>
              <FormRow>
                <FormField>
                  <FormLabel>
                    First Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChange={e => handleInputChange('firstName', e.target.value)}
                    hasError={!!formErrors.firstName}
                  />
                  {formErrors.firstName && <FormError>{formErrors.firstName}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Last Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChange={e => handleInputChange('lastName', e.target.value)}
                    hasError={!!formErrors.lastName}
                  />
                  {formErrors.lastName && <FormError>{formErrors.lastName}</FormError>}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Email <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <FormInput
                  type="email"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  hasError={!!formErrors.email}
                />
                {formErrors.email && <FormError>{formErrors.email}</FormError>}
              </FormField>

              <FormField>
                <FormLabel>Phone Number</FormLabel>
                <FormInput
                  type="tel"
                  placeholder="Enter phone number (optional)"
                  value={formData.phone}
                  onChange={e => handleInputChange('phone', e.target.value)}
                />
              </FormField>

              <FormRow>
                <FormField>
                  <FormLabel>
                    Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Enter password"
                    value={formData.password}
                    onChange={e => handleInputChange('password', e.target.value)}
                    hasError={!!formErrors.password}
                  />
                  {formErrors.password && <FormError>{formErrors.password}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Confirm Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Confirm password"
                    value={formData.confirmPassword}
                    onChange={e => handleInputChange('confirmPassword', e.target.value)}
                    hasError={!!formErrors.confirmPassword}
                  />
                  {formErrors.confirmPassword && (
                    <FormError>{formErrors.confirmPassword}</FormError>
                  )}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Organizations <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <OrganizationCheckboxContainer>
                  {organizations.map(org => (
                    <CheckboxItem key={org.id}>
                      <Checkbox
                        type="checkbox"
                        checked={selectedOrganizations.includes(org.id)}
                        onChange={() => handleOrganizationToggle(org.id)}
                      />
                      <CheckboxLabel>{org.name}</CheckboxLabel>
                    </CheckboxItem>
                  ))}
                </OrganizationCheckboxContainer>
                {formErrors.organizations && <FormError>{formErrors.organizations}</FormError>}
              </FormField>
            </FormContainer>

            <ModalActions>
              <ModalButton onClick={handleCloseAddModal}>Cancel</ModalButton>
              <ModalButton
                variant="primary"
                onClick={handleAddAdmin}
                disabled={addingAdmin || !isFormValid()}
              >
                {addingAdmin ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus size={16} />
                    Create Admin
                  </>
                )}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}

      {/* Edit Admin Modal */}
      {showEditModal && editingAdminData && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Edit Admin</ModalTitle>
              <CloseButton onClick={handleCloseEditModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <FormContainer>
              <FormRow>
                <FormField>
                  <FormLabel>
                    First Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChange={e => handleInputChange('firstName', e.target.value)}
                    hasError={!!formErrors.firstName}
                  />
                  {formErrors.firstName && <FormError>{formErrors.firstName}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Last Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChange={e => handleInputChange('lastName', e.target.value)}
                    hasError={!!formErrors.lastName}
                  />
                  {formErrors.lastName && <FormError>{formErrors.lastName}</FormError>}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Email <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <FormInput
                  type="email"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  hasError={!!formErrors.email}
                />
                {formErrors.email && <FormError>{formErrors.email}</FormError>}
              </FormField>

              <FormField>
                <FormLabel>Phone Number</FormLabel>
                <FormInput
                  type="tel"
                  placeholder="Enter phone number (optional)"
                  value={formData.phone}
                  onChange={e => handleInputChange('phone', e.target.value)}
                />
              </FormField>
              <FormRow>
                <FormField>
                  <FormLabel>
                    New Password
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Enter new password (optional)"
                    value={formData.password}
                    onChange={e => handleInputChange('password', e.target.value)}
                    hasError={!!formErrors.password}
                  />
                  {formErrors.password && <FormError>{formErrors.password}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Confirm New Password
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Confirm new password"
                    value={formData.confirmPassword}
                    onChange={e => handleInputChange('confirmPassword', e.target.value)}
                    hasError={!!formErrors.confirmPassword}
                  />
                  {formErrors.confirmPassword && (
                    <FormError>{formErrors.confirmPassword}</FormError>
                  )}
                </FormField>
              </FormRow>
            </FormContainer>

            <ModalActions>
              <ModalButton onClick={handleCloseEditModal}>Cancel</ModalButton>
              <ModalButton
                variant="primary"
                onClick={handleUpdateAdmin}
                disabled={editingAdmin}
              >
                {editingAdmin ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit size={16} />
                    Update Admin
                  </>
                )}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}

      {/* Manage Organizations Modal */}
      {showManageOrgsModal && managingAdmin && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Manage Organizations - {managingAdmin.user.firstName} {managingAdmin.user.lastName}
              </ModalTitle>
              <CloseButton onClick={handleCloseManageOrgsModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            {loadingAdminOrgs ? (
              <LoadingContainer>
                <Loader2 size={24} className="animate-spin" />
                <span>Loading organizations...</span>
              </LoadingContainer>
            ) : (
              <FormContainer>
                <FormField>
                  <FormLabel>
                    Select Organizations <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <OrganizationCheckboxContainer>
                    {organizations.map(org => (
                      <CheckboxItem key={org.id}>
                        <Checkbox
                          type="checkbox"
                          checked={selectedOrganizations.includes(org.id)}
                          onChange={() => handleOrganizationToggle(org.id)}
                        />
                        <CheckboxLabel>{org.name}</CheckboxLabel>
                      </CheckboxItem>
                    ))}
                  </OrganizationCheckboxContainer>
                  {organizations.length === 0 && (
                    <FormError>No organizations available to manage.</FormError>
                  )}
                </FormField>

                {adminOrganizations.length > 0 && (
                  <FormField>
                    <FormLabel>Current Organizations</FormLabel>
                    <div style={{ padding: '12px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
                      {adminOrganizations.map(org => (
                        <div key={org.id} style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          marginBottom: '8px'
                        }}>
                          <Building size={16} />
                          <span>{org.name}</span>
                          <span style={{
                            fontSize: '12px',
                            color: '#6b7280',
                            marginLeft: 'auto'
                          }}>
                            Assigned: {org.assignedAt ? formatDate(org.assignedAt) : 'Unknown'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </FormField>
                )}
              </FormContainer>
            )}

            <ModalActions>
              <ModalButton onClick={handleCloseManageOrgsModal}>Cancel</ModalButton>
              <ModalButton
                variant="primary"
                onClick={handleUpdateAdminOrganizations}
                disabled={updatingOrganizations || loadingAdminOrgs}
              >
                {updatingOrganizations ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Building size={16} />
                    Update Organizations
                  </>
                )}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}
    </SettingsLayout>
  );
}
