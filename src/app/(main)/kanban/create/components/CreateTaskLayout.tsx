'use client';

import React, { useState, useEffect, useMemo } from 'react';
import styled from 'styled-components';
import { ArrowLeft, Loader, Plus, X, Search, Filter } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { appTheme } from '@/app/theme';
import TextEditor from '@/components/TextEditor';
import { processImagesInContent, createImageUploadFunction } from '@/utils/imageUpload';
import useUserStore from '@/store/userStore';

// Types
interface User {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  organization?: Organization;
}

interface ApiMember {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  departmentId: number;
  isLeader: boolean;
  department: {
    id: number;
    name: string;
    organization: {
      id: number;
      name: string;
    };
  };
}

// Styled components based on RankingLayout
const CreateTaskContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${appTheme.spacing.xl};
  gap: ${appTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${appTheme.borderRadius.lg};

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${appTheme.spacing.lg};
    gap: ${appTheme.spacing.lg};
    max-width: 100%;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: ${appTheme.spacing.md};
    gap: ${appTheme.spacing.md};
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.sm};
  }
`;

const CreateTaskHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.lg};
  background-color: ${appTheme.colors.background.main};
  box-shadow: ${appTheme.shadows.sm};

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${appTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: 767px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${appTheme.spacing.sm};
    padding: ${appTheme.spacing.md};
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.xs};
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};

  /* Mobile */
  @media (max-width: 767px) {
    gap: ${appTheme.spacing.sm};
    width: 100%;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: ${appTheme.spacing.xs};
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.sm};
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${appTheme.borderRadius.md};
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  transition: ${appTheme.transitions.default};

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: ${appTheme.colors.text.primary};
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: ${appTheme.spacing.xs};
    min-width: 44px; /* Touch-friendly size */
    min-height: 44px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${appTheme.spacing.xs};
    min-width: 40px;
    min-height: 40px;
  }
`;

const CreateTaskTitle = styled.h1`
  font-size: ${appTheme.typography.fontSizes['3xl']};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: ${appTheme.typography.fontSizes['2xl']};
    gap: ${appTheme.spacing.sm};
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: ${appTheme.typography.fontSizes.xl};
    gap: ${appTheme.spacing.sm};
    text-align: center;
    justify-content: center;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: ${appTheme.typography.fontSizes.lg};
    gap: ${appTheme.spacing.xs};
  }
`;

const CreateTaskContent = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const ContentArea = styled.div`
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  background-color: ${appTheme.colors.background.main};
  box-shadow: ${appTheme.shadows.sm};
  overflow: hidden;
`;

const FormContainer = styled.form`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: ${appTheme.spacing.lg};

  /* Tablet */
  @media (max-width: 1023px) {
    gap: ${appTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    gap: ${appTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: ${appTheme.spacing.xs};
  }
`;

const FormGroup = styled.div<{ $fullWidth?: boolean; $spanTwo?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  margin-bottom: 0.5rem;

  ${props =>
    props.$fullWidth &&
    `
    grid-column: 1 / -1;
  `}

  ${props =>
    props.$spanTwo &&
    `
    grid-column: span 2;

    @media (max-width: ${appTheme.breakpoints.lg}) {
      grid-column: 1 / -1;
    }
  `}

  /* Mobile */
  @media (max-width: 767px) {
    gap: 0.25rem;
    margin-bottom: 0.375rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: 0.2rem;
    margin-bottom: 0.25rem;
  }
`;

const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  display: block;

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 0.8rem;
    margin-bottom: 0.2rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 0.75rem;
    margin-bottom: 0.15rem;
  }
`;

const FormInput = styled.input`
  padding: ${appTheme.spacing.md};
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.sm};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: #6366f1;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &:hover:not(:disabled) {
    border-color: #9ca3af;
  }

  &::placeholder {
    color: ${appTheme.colors.text.tertiary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #e5e7eb;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${appTheme.spacing.sm};
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: ${appTheme.spacing.sm};
    font-size: ${appTheme.typography.fontSizes.xs};
    min-height: 44px; /* Touch-friendly height */
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${appTheme.spacing.xs};
    font-size: ${appTheme.typography.fontSizes.xs};
  }
`;

const FormSelect = styled.select`
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  appearance: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;

  &:hover:not(:disabled) {
    border-color: #9ca3af;
    background-color: #f9fafb;
  }

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: white;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: #e5e7eb;
    background-color: #f9fafb;
    color: #9ca3af;
  }

  option {
    padding: 0.5rem;
    color: #111827;
    background-color: white;
  }

  option:checked {
    background-color: #3b82f6;
    color: white;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 0.625rem 2.25rem 0.625rem 0.625rem;
    font-size: 0.8rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 0.5rem 2rem 0.5rem 0.5rem;
    font-size: 0.75rem;
    min-height: 44px; /* Touch-friendly height */
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 0.5rem 1.75rem 0.5rem 0.5rem;
    font-size: 0.7rem;
  }
`;

const FormActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
  padding-top: ${appTheme.spacing.lg};
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  grid-column: 1 / -1;

  /* Tablet */
  @media (max-width: 1023px) {
    gap: ${appTheme.spacing.sm};
    padding-top: ${appTheme.spacing.md};
  }

  /* Mobile */
  @media (max-width: 767px) {
    flex-direction: column;
    gap: ${appTheme.spacing.sm};
    padding-top: ${appTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: 480px) {
    gap: ${appTheme.spacing.xs};
    padding-top: ${appTheme.spacing.xs};
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  border: none;

  ${props =>
    props.$variant === 'primary'
      ? `
    background: linear-gradient(135deg, rgb(255, 255, 255), rgb(244, 244, 244));
    color: #000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
  `
      : `
    background: rgba(255, 255, 255, 0.1);
    color: ${appTheme.colors.text.secondary};
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.2);
      color: ${appTheme.colors.text.primary};
    }
  `}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  /* Tablet */
  @media (max-width: 1023px) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    gap: ${appTheme.spacing.xs};
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    font-size: ${appTheme.typography.fontSizes.xs};
    width: 100%;
    justify-content: center;
    min-height: 44px; /* Touch-friendly height */

    /* Reduce hover effects on mobile */
    &:hover:not(:disabled) {
      transform: none;
    }
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.xs};
  }
`;

const ErrorMessage = styled.div`
  color: #ff6b6b;
  font-size: ${appTheme.typography.fontSizes.sm};
  padding: ${appTheme.spacing.sm};
  background-color: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: ${appTheme.borderRadius.md};
`;

const LoadingSpinner = styled(Loader)`
  animation: spin 1s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

// New styled components for user list UI
const UserListContainer = styled.div`
  border: 1px solid rgba(209, 213, 219, 0.6);
  border-radius: ${appTheme.borderRadius.lg};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  min-height: 80px;
  max-height: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const UserListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8), rgba(243, 244, 246, 0.6));
  backdrop-filter: blur(5px);
  border-radius: ${appTheme.borderRadius.lg} ${appTheme.borderRadius.lg} 0 0;
`;

const UserListTitle = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const AddUserButton = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  user-select: none;

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

const UserListContent = styled.div`
  padding: ${appTheme.spacing.sm};
  overflow-y: auto;
  flex: 1;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;

    &:hover {
      background: rgba(156, 163, 175, 0.7);
    }
  }
`;

const UserItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  margin-bottom: ${appTheme.spacing.xs};
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.9));
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  gap: ${appTheme.spacing.sm};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease-in-out;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));

    &::before {
      left: 100%;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'medium' }>`
  width: ${props => (props.$size === 'small' ? '32px' : '36px')};
  height: ${props => (props.$size === 'small' ? '32px' : '36px')};
  border-radius: 50%;
  background: ${props =>
    props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #6366f1, #8b5cf6)'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  font-size: ${props =>
    props.$size === 'small' ? appTheme.typography.fontSizes.xs : appTheme.typography.fontSizes.sm};
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
`;

const UserName = styled.span`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: #1f2937;
  letter-spacing: -0.025em;
`;

const UserEmail = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #6b7280;
  font-weight: 400;
`;

const UserDepartmentOrg = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #9ca3af;
  font-weight: 400;
  font-style: italic;
`;

const LeaderBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background-color: #fbbf24;
  color: #92400e;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  border-radius: 4px;
  margin-left: 6px;
`;

const UserActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const LeaderCheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  cursor: pointer;
  user-select: none;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  background: rgba(99, 102, 241, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);

  &:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
  }
`;

const LeaderText = styled.span`
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: #4f46e5;
  letter-spacing: 0.025em;
`;

const RemoveUserButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #ef4444;
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const EmptyUserList = styled.div`
  text-align: center;
  padding: ${appTheme.spacing.md};
  color: #9ca3af;
  font-size: ${appTheme.typography.fontSizes.xs};
  font-style: italic;
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.5), rgba(243, 244, 246, 0.3));
  border-radius: ${appTheme.borderRadius.md};
  margin: ${appTheme.spacing.xs};
  border: 1px dashed rgba(156, 163, 175, 0.3);
`;

// Modal styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.xl};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${appTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${appTheme.spacing.lg};
`;

const ModalTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.bold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${appTheme.typography.fontSizes.xl};
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: #f3f4f6;
    color: ${appTheme.colors.text.primary};
  }
`;

const FilterContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.md};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  & > div:last-child {
    grid-column: 1 / -1;
  }
`;

const ModalSearchInputContainer = styled.div`
  position: relative;
`;

const ModalSearchInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.sm} ${appTheme.spacing.sm} 2.25rem;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.sm};
  background-color: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.sm};
  transition: ${appTheme.transitions.default};
  height: 36px;

  &:focus {
    outline: none;
    border-color: #6366f1;
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const ModalSearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
`;

const ClearFiltersButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: ${appTheme.borderRadius.sm};
  font-size: ${appTheme.typography.fontSizes.xs};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  margin-bottom: ${appTheme.spacing.sm};
  height: 32px;
  font-weight: 500;

  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
  }
`;

const FilterSummary = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: ${appTheme.borderRadius.sm};
  margin-bottom: ${appTheme.spacing.sm};
  font-size: ${appTheme.typography.fontSizes.xs};
  color: #1d4ed8;
  font-weight: 500;
`;

const SearchResultsContainer = styled.div`
  max-height: 280px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: ${appTheme.borderRadius.sm};
`;

const SearchResultItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8fafc;
  }
`;

// Custom Checkbox Component
const CheckboxContainer = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;

  &:hover .checkbox-box {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: scale(1.05);
  }
`;

const CheckboxInput = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;

  &:disabled + .checkbox-box {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f3f4f6;
    border-color: #d1d5db;
  }

  &:disabled ~ .checkbox-container {
    cursor: not-allowed;
  }
`;

const CheckboxBox = styled.div<{ $checked: boolean; $disabled: boolean }>`
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid ${props => (props.$checked ? '#6366f1' : '#d1d5db')};
  border-radius: 6px;
  background: ${props =>
    props.$checked
      ? 'linear-gradient(135deg, #6366f1, #4f46e5)'
      : 'linear-gradient(135deg, #ffffff, #f8fafc)'};
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${props =>
    props.$checked ? '0 2px 8px rgba(99, 102, 241, 0.3)' : '0 1px 3px rgba(0, 0, 0, 0.1)'};

  ${props =>
    props.$disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
    border-color: #d1d5db;
    box-shadow: none;
  `}

  &::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2.5px 2.5px 0;
    transform: rotate(45deg) scale(${props => (props.$checked ? '1' : '0')});
    transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    top: 2px;
    left: 6px;
  }
`;

interface CustomCheckboxProps {
  checked: boolean;
  onChange: () => void;
  disabled?: boolean;
  className?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  checked,
  onChange,
  disabled = false,
  className,
}) => {
  return (
    <CheckboxContainer
      className={`checkbox-container ${className || ''}`}
      onClick={disabled ? undefined : onChange}
    >
      <CheckboxInput type="checkbox" checked={checked} onChange={onChange} disabled={disabled} />
      <CheckboxBox className="checkbox-box" $checked={checked} $disabled={disabled} />
    </CheckboxContainer>
  );
};

const AddToListButton = styled.button`
  background-color: #10b981;
  color: white;
  border: none;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.sm};
  font-size: ${appTheme.typography.fontSizes.xs};
  cursor: pointer;
  transition: ${appTheme.transitions.default};

  &:hover {
    background-color: #059669;
  }

  &:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }
`;

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

export default function CreateTaskLayout() {
  const router = useRouter();
  const { getToken } = useAuth();
  const { userData } = useUserStore();

  const [formData, setFormData] = useState({
    taskTitle: '',
    taskDescription: '',
    organizationId: '',
    departmentId: '',
    points: '',
    dueDate: '',
  });
  const [assignedToUserIds, setAssignedToUserIds] = useState<string[]>([]);
  const [taskAssignments, setTaskAssignments] = useState<
    Array<{ userId: string; isLeader: boolean }>
  >([]);
  const [assignedUsers, setAssignedUsers] = useState<User[]>([]);
  const [members, setMembers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchFilters, setSearchFilters] = useState({
    organizationId: '',
    departmentId: '',
    name: '',
  });
  const [modalOrganizations, setModalOrganizations] = useState<Organization[]>([]);
  const [modalDepartments, setModalDepartments] = useState<Department[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Create upload function for TextEditor
  const uploadImageFunction = useMemo(() => {
    return async (file: File): Promise<string> => {
      const token = await getToken();
      if (!token) {
        throw new Error('Authentication required');
      }
      return createImageUploadFunction(token)(file);
    };
  }, [getToken]);

  // Set organizations from userData.organizations on component mount
  useEffect(() => {
    if (userData?.organizations) {
      // Map userData.organizations to the format expected by form
      const orgs = userData.organizations.map(org => ({
        id: org.id,
        name: org.name,
      }));
      setOrganizations(orgs);
    } else {
      setOrganizations([]);
    }
  }, [userData]);

  // Fetch departments when organization is selected
  useEffect(() => {
    const fetchDepartments = async () => {
      if (formData.organizationId) {
        try {
          const token = await getToken();
          if (!token) return;

          const orgId = Number(formData.organizationId);
          const response = await fetch(`/api/v1/department?organizationId=${orgId}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            throw new Error('Failed to fetch departments');
          }

          const data = await response.json();
          setDepartments(data.departments || []);

          // Reset department and member selection
          setFormData(prev => ({
            ...prev,
            departmentId: '',
            assignedToUserId: '',
          }));
        } catch (err) {
          console.error('Error fetching departments:', err);
          setDepartments([]);
        }
      } else {
        setDepartments([]);
      }
    };

    fetchDepartments();
  }, [formData.organizationId, getToken]);

  // Fetch members based on selected department
  useEffect(() => {
    const fetchMembers = async () => {
      if (formData.departmentId) {
        try {
          const token = await getToken();
          if (!token) return;

          const deptId = Number(formData.departmentId);
          const response = await fetch(`/api/v1/member?departmentId=${deptId}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            throw new Error('Failed to fetch members');
          }

          const data = await response.json();
          const apiMembers = data.members || [];

          // Transform API response to match our User interface
          const transformedMembers = apiMembers.map((member: ApiMember) => ({
            id: member.user.id,
            name: `${member.user.firstName} ${member.user.lastName}`,
            email: member.user.email,
            imageUrl: member.user.imageUrl,
            departmentName: member.department?.name,
            organizationName: member.department?.organization?.name,
            isLeader: member.isLeader,
          }));

          setMembers(transformedMembers);

          // Reset member selection
          setFormData(prev => ({
            ...prev,
            assignedToUserId: '',
          }));
        } catch (err) {
          console.error('Error fetching members:', err);
          setMembers([]);
        }
      } else {
        setMembers([]);
      }
    };

    fetchMembers();
  }, [formData.departmentId, getToken]);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle text editor content changes
  const handleDescriptionChange = (content: string) => {
    setFormData(prev => ({
      ...prev,
      taskDescription: content,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    // Validate form
    if (!formData.taskTitle.trim()) {
      setFormError('Task title is required');
      return;
    }

    if (!formData.organizationId) {
      setFormError('Please select an organization');
      return;
    }

    if (!formData.departmentId) {
      setFormError('Please select a department');
      return;
    }

    if (assignedToUserIds.length === 0) {
      setFormError('Please select at least one assignee');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = await getToken();

      if (!token) {
        setFormError('Authentication required');
        return;
      }

      // Process images in task description (upload base64 images to server)
      const processedDescription = await processImagesInContent(formData.taskDescription, token);

      // Prepare data for API
      const payload = {
        taskTitle: formData.taskTitle.trim(),
        taskDescription: processedDescription,
        statusId: 1, // Auto-set to To Do status
        assignedToUserIds: assignedToUserIds.map(id => Number(id)),
        taskAssignments: taskAssignments.map(assignment => ({
          userId: parseInt(assignment.userId),
          isLeader: assignment.isLeader,
        })),
        organizationId: Number(formData.organizationId),
        departmentId: Number(formData.departmentId),
        points: formData.points ? Number(formData.points) : null,
        dueDate: formData.dueDate || null,
      };

      // Send request to create task
      const response = await fetch('/api/v1/task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Create task API error:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          payload,
        });
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create task`);
      }

      const taskResult = await response.json();
      const createdTask = taskResult.task;

      // Create task chat automatically
      try {
        const chatPayload = {
          name: `${createdTask.id}-${formData.taskTitle}`,
          chatType: 'task',
          taskId: createdTask.id,
          organizationId: null,
          departmentId: null,
        };

        const chatResponse = await fetch('/api/v1/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(chatPayload),
        });

        if (!chatResponse.ok) {
          console.error('Failed to create task chat:', await chatResponse.json());
          // Don't throw error here - task creation was successful
        } else {
          console.log('Task chat created successfully');
        }
      } catch (chatError) {
        console.error('Error creating task chat:', chatError);
        // Don't throw error here - task creation was successful
      }

      // Success - redirect back to kanban board
      router.push('/kanban');
    } catch (err) {
      console.error('Error creating task:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to create task');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    router.push('/kanban');
  };

  // Modal functions
  const openModal = async () => {
    setIsModalOpen(true);

    // Use organizations from userData.organizations instead of API
    if (modalOrganizations.length === 0 && userData?.organizations) {
      try {
        // Map userData.organizations to the format expected by modal
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        setModalOrganizations(orgs);

        // If there are organizations, set the first one as default and trigger search
        if (orgs.length > 0) {
          const firstOrgId = orgs[0].id.toString();
          setSearchFilters(prev => ({ ...prev, organizationId: firstOrgId }));

          // Fetch departments for the first organization (still from API)
          const token = await getToken();
          if (token) {
            const deptResponse = await fetch(`/api/v1/department?organizationId=${firstOrgId}`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (deptResponse.ok) {
              const deptData = await deptResponse.json();
              setModalDepartments(deptData.departments || []);
            }
          }

          // Trigger initial search with first organization
          searchUsers({ organizationId: firstOrgId, departmentId: '', name: '' });
        }
      } catch (err) {
        console.error('Error setting up organizations for modal:', err);
      }
    } else if (modalOrganizations.length > 0) {
      // If organizations are already loaded, trigger search with current filters or first org
      const currentOrg = searchFilters.organizationId || modalOrganizations[0]?.id.toString();
      if (currentOrg) {
        searchUsers({ ...searchFilters, organizationId: currentOrg });
      }
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSearchResults([]);
    setSearchFilters({ organizationId: '', departmentId: '', name: '' });
    setModalDepartments([]);
    setSelectedUsers(new Set());
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      setSearchTimeout(null);
    }
  };

  // Debounced search function
  const debouncedSearch = (filters: typeof searchFilters) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      searchUsers(filters);
    }, 500); // 500ms debounce

    setSearchTimeout(timeout);
  };

  // Handle search filter changes
  const handleSearchFilterChange = async (field: string, value: string) => {
    const newFilters = { ...searchFilters, [field]: value };
    setSearchFilters(newFilters);

    // If organization changed, fetch departments for that organization
    if (field === 'organizationId' && value) {
      try {
        const token = await getToken();
        if (!token) return;

        const response = await fetch(`/api/v1/department?organizationId=${value}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setModalDepartments(data.departments || []);
        }
      } catch (err) {
        console.error('Error fetching departments for modal:', err);
        setModalDepartments([]);
      }
    } else if (field === 'organizationId' && !value) {
      setModalDepartments([]);
      newFilters.departmentId = '';
    }

    // Trigger debounced search if we have search criteria
    if (newFilters.organizationId || newFilters.departmentId || newFilters.name.trim()) {
      debouncedSearch(newFilters);
    } else {
      setSearchResults([]);
    }
  };

  // Search for users
  const searchUsers = async (filters = searchFilters) => {
    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const token = await getToken();
      if (!token) return;

      let url = '/api/v1/member?';
      const params = new URLSearchParams();

      if (filters.departmentId) {
        params.append('departmentId', filters.departmentId);
      } else if (filters.organizationId) {
        params.append('organizationId', filters.organizationId);
      }

      if (filters.name.trim()) {
        params.append('name', filters.name.trim());
      }

      const response = await fetch(url + params.toString(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const apiMembers = data.members || [];

        // Transform API response to match our User interface
        const transformedMembers: User[] = apiMembers.map((member: ApiMember) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
          departmentName: member.department?.name,
          organizationName: member.department?.organization?.name,
          isLeader: member.isLeader,
        }));

        setSearchResults(transformedMembers);
      }
    } catch (err) {
      console.error('Error searching users:', err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Toggle user selection in modal
  const toggleUserSelection = (user: User) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(user.id)) {
        newSet.delete(user.id);
      } else {
        newSet.add(user.id);
      }
      return newSet;
    });
  };

  // Add selected users to assigned list
  const addSelectedUsersToList = () => {
    const usersToAdd = searchResults.filter(
      user => selectedUsers.has(user.id) && !assignedUsers.find(u => u.id === user.id)
    );

    if (usersToAdd.length > 0) {
      setAssignedUsers(prev => [...prev, ...usersToAdd]);
      setAssignedToUserIds(prev => [...prev, ...usersToAdd.map(u => u.id.toString())]);
      setTaskAssignments(prev => [
        ...prev,
        ...usersToAdd.map(u => ({ userId: u.id.toString(), isLeader: false })),
      ]);
    }

    closeModal();
  };

  // Add user to assigned list (keep for backward compatibility)
  const addUserToList = (user: User) => {
    if (!assignedUsers.find(u => u.id === user.id)) {
      setAssignedUsers(prev => [...prev, user]);
      setAssignedToUserIds(prev => [...prev, user.id.toString()]);
      setTaskAssignments(prev => [...prev, { userId: user.id.toString(), isLeader: false }]);
    }
  };

  // Clear all search filters
  const clearSearchFilters = () => {
    setSearchFilters({
      organizationId: '',
      departmentId: '',
      name: '',
    });
    setSearchResults([]);
    setSelectedUsers(new Set());
    setModalDepartments([]);
  };

  // Remove user from assigned list
  const removeUserFromList = (userId: number) => {
    setAssignedUsers(prev => prev.filter(u => u.id !== userId));
    setAssignedToUserIds(prev => prev.filter(id => id !== userId.toString()));
    setTaskAssignments(prev => prev.filter(assignment => assignment.userId !== userId.toString()));
  };

  return (
    <CreateTaskContainer>
      <CreateTaskHeader>
        <HeaderLeft>
          <BackButton onClick={handleBack}>
            <ArrowLeft size={18} />
          </BackButton>
          <CreateTaskTitle>Create New Task</CreateTaskTitle>
        </HeaderLeft>
      </CreateTaskHeader>

      <CreateTaskContent>
        <ContentArea>
          <FormContainer onSubmit={handleSubmit}>
            <FormGroup $fullWidth>
              <FormLabel htmlFor="taskTitle">Task Title *</FormLabel>
              <FormInput
                id="taskTitle"
                name="taskTitle"
                value={formData.taskTitle}
                onChange={handleInputChange}
                placeholder="Enter task title"
                required
              />
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="organizationId">Organization *</FormLabel>
              <FormSelect
                id="organizationId"
                name="organizationId"
                value={formData.organizationId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Organization</option>
                {organizations.map(org => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>

            <FormGroup>
              <FormLabel htmlFor="departmentId">Department *</FormLabel>
              <FormSelect
                id="departmentId"
                name="departmentId"
                value={formData.departmentId}
                onChange={handleInputChange}
                required
                disabled={!formData.organizationId}
              >
                <option value="">
                  {formData.organizationId ? 'Select Department' : 'Select an organization first'}
                </option>
                {departments.map(dept => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>
            <FormGroup>
              <FormLabel htmlFor="points">Points</FormLabel>
              <FormInput
                id="points"
                name="points"
                type="number"
                min="0"
                value={formData.points}
                onChange={handleInputChange}
                placeholder="Enter story points"
              />
            </FormGroup>
            <FormGroup>
              <FormLabel htmlFor="dueDate">Due Date</FormLabel>
              <FormInput
                id="dueDate"
                name="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={handleInputChange}
              />
            </FormGroup>

            <FormGroup $fullWidth>
              <FormLabel>Assigned Users</FormLabel>
              <UserListContainer>
                <UserListHeader>
                  <UserListTitle>
                    {assignedUsers.length} user{assignedUsers.length !== 1 ? 's' : ''} assigned
                  </UserListTitle>
                  <AddUserButton onClick={openModal}>
                    <Plus size={14} />
                    Add User
                  </AddUserButton>
                </UserListHeader>
                <UserListContent>
                  {assignedUsers.length === 0 ? (
                    <EmptyUserList>No users assigned yet</EmptyUserList>
                  ) : (
                    assignedUsers.map(user => (
                      <UserItem key={user.id}>
                        <UserAvatar $imageUrl={user.imageUrl}>
                          {!user.imageUrl && getUserInitials(user.name)}
                        </UserAvatar>
                        <UserInfo>
                          <UserName>
                            {user.name}
                            {user.isLeader && (
                              <LeaderBadge>Leader</LeaderBadge>
                            )}
                          </UserName>
                          <UserEmail>{user.email}</UserEmail>
                          {(user.departmentName || user.organizationName) && (
                            <UserDepartmentOrg>
                              {user.departmentName && user.organizationName
                                ? `${user.departmentName} • ${user.organizationName}`
                                : user.departmentName || user.organizationName}
                            </UserDepartmentOrg>
                          )}
                        </UserInfo>
                        <UserActions>
                          <LeaderCheckboxLabel>
                            <CustomCheckbox
                              checked={
                                taskAssignments.find(
                                  assignment => assignment.userId === user.id.toString()
                                )?.isLeader || false
                              }
                              onChange={() => {
                                const userId = user.id.toString();
                                setTaskAssignments(prev =>
                                  prev.map(assignment =>
                                    assignment.userId === userId
                                      ? { ...assignment, isLeader: !assignment.isLeader }
                                      : assignment
                                  )
                                );
                              }}
                            />
                            <LeaderText>Leader</LeaderText>
                          </LeaderCheckboxLabel>
                          <RemoveUserButton onClick={() => removeUserFromList(user.id)}>
                            ×
                          </RemoveUserButton>
                        </UserActions>
                      </UserItem>
                    ))
                  )}
                </UserListContent>
              </UserListContainer>
            </FormGroup>

            <FormGroup $fullWidth>
              <FormLabel htmlFor="taskDescription">Description</FormLabel>
              <TextEditor
                value={formData.taskDescription}
                onChange={handleDescriptionChange}
                placeholder="Enter task description"
                minHeight={150}
                maxHeight={1200}
                uploadImage={uploadImageFunction}
              />
            </FormGroup>

            {formError && <ErrorMessage>{formError}</ErrorMessage>}

            <FormActions>
              <Button type="button" $variant="secondary" onClick={handleBack}>
                Cancel
              </Button>
              <Button type="submit" $variant="primary" disabled={isSubmitting}>
                {isSubmitting && <LoadingSpinner size={16} />}
                {isSubmitting ? 'Creating...' : 'Create Task'}
              </Button>
            </FormActions>
          </FormContainer>
        </ContentArea>
      </CreateTaskContent>

      {/* User Search Modal */}
      {isModalOpen && (
        <ModalOverlay onClick={closeModal}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Find Users</ModalTitle>
              <CloseButton onClick={closeModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>
            <FilterContainer>
              <FormGroup>
                <FormLabel>Organization</FormLabel>
                <FormSelect
                  value={searchFilters.organizationId}
                  onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
                >
                  <option value="">All Organizations</option>
                  {modalOrganizations.map(org => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </FormSelect>
              </FormGroup>

              <FormGroup>
                <FormLabel>Department</FormLabel>
                <FormSelect
                  value={searchFilters.departmentId}
                  onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                  disabled={!searchFilters.organizationId}
                >
                  <option value="">
                    {searchFilters.organizationId ? 'All Departments' : 'Select organization first'}
                  </option>
                  {modalDepartments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </FormSelect>
              </FormGroup>

              <FormGroup>
                <FormLabel>Name</FormLabel>
                <ModalSearchInputContainer>
                  <ModalSearchIcon>
                    <Search size={16} />
                  </ModalSearchIcon>
                  <ModalSearchInput
                    type="text"
                    placeholder="Search by name or email..."
                    value={searchFilters.name}
                    onChange={e => handleSearchFilterChange('name', e.target.value)}
                  />
                </ModalSearchInputContainer>
              </FormGroup>
            </FilterContainer>

            {(searchFilters.organizationId ||
              searchFilters.departmentId ||
              searchFilters.name.trim()) && (
              <>
                <ClearFiltersButton onClick={clearSearchFilters}>
                  <X size={14} />
                  Clear Filters
                </ClearFiltersButton>

                <FilterSummary>
                  <Filter size={14} />
                  <span>
                    Filtering by:{' '}
                    {searchFilters.organizationId && (
                      <span>
                        {modalOrganizations.find(
                          org => org.id.toString() === searchFilters.organizationId
                        )?.name || 'Organization'}
                        {(searchFilters.departmentId || searchFilters.name.trim()) && ', '}
                      </span>
                    )}
                    {searchFilters.departmentId && (
                      <span>
                        {modalDepartments.find(
                          dept => dept.id.toString() === searchFilters.departmentId
                        )?.name || 'Department'}
                        {searchFilters.name.trim() && ', '}
                      </span>
                    )}
                    {searchFilters.name.trim() && <span>Name: "{searchFilters.name}"</span>}
                  </span>
                </FilterSummary>
              </>
            )}

            {isSearching && (
              <div
                style={{
                  textAlign: 'center',
                  padding: appTheme.spacing.sm,
                  color: appTheme.colors.text.secondary,
                  fontSize: appTheme.typography.fontSizes.sm,
                }}
              >
                <LoadingSpinner size={14} style={{ marginRight: appTheme.spacing.xs }} />
                Searching...
              </div>
            )}

            {searchResults.length > 0 && (
              <>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: appTheme.spacing.sm,
                    padding: `${appTheme.spacing.xs} ${appTheme.spacing.sm}`,
                    backgroundColor: '#f8fafc',
                    borderRadius: appTheme.borderRadius.sm,
                  }}
                >
                  <span
                    style={{
                      fontSize: appTheme.typography.fontSizes.xs,
                      color: appTheme.colors.text.secondary,
                      fontWeight: 500,
                    }}
                  >
                    {selectedUsers.size} of {searchResults.length} selected
                  </span>
                  {selectedUsers.size > 0 && (
                    <Button
                      type="button"
                      onClick={addSelectedUsersToList}
                      style={{
                        padding: `${appTheme.spacing.xs} ${appTheme.spacing.sm}`,
                        fontSize: appTheme.typography.fontSizes.xs,
                        height: '28px',
                      }}
                    >
                      Add ({selectedUsers.size})
                    </Button>
                  )}
                </div>
                <SearchResultsContainer>
                  {searchResults.map(user => {
                    const isAlreadyAssigned = assignedUsers.some(u => u.id === user.id);
                    const isSelected = selectedUsers.has(user.id);

                    return (
                      <SearchResultItem key={user.id}>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: appTheme.spacing.sm,
                          }}
                        >
                          <CustomCheckbox
                            checked={isSelected}
                            onChange={() => toggleUserSelection(user)}
                            disabled={isAlreadyAssigned}
                          />
                          <UserAvatar $imageUrl={user.imageUrl} $size="small">
                            {!user.imageUrl && getUserInitials(user.name)}
                          </UserAvatar>
                          <UserInfo>
                            <UserName style={{ opacity: isAlreadyAssigned ? 0.5 : 1 }}>
                              {user.name}
                              {user.isLeader && (
                                <LeaderBadge style={{ opacity: isAlreadyAssigned ? 0.5 : 1 }}>
                                  Leader
                                </LeaderBadge>
                              )}
                              {isAlreadyAssigned && (
                                <span style={{ fontSize: '12px', color: '#10b981' }}>
                                  {' '}
                                  (Already assigned)
                                </span>
                              )}
                            </UserName>
                            <UserEmail style={{ opacity: isAlreadyAssigned ? 0.5 : 1 }}>
                              {user.email}
                            </UserEmail>
                            {(user.departmentName || user.organizationName) && (
                              <UserDepartmentOrg style={{ opacity: isAlreadyAssigned ? 0.5 : 1 }}>
                                {user.departmentName && user.organizationName
                                  ? `${user.departmentName} • ${user.organizationName}`
                                  : user.departmentName || user.organizationName}
                              </UserDepartmentOrg>
                            )}
                          </UserInfo>
                        </div>
                      </SearchResultItem>
                    );
                  })}
                </SearchResultsContainer>
              </>
            )}

            {searchResults.length === 0 &&
              (searchFilters.organizationId ||
                searchFilters.departmentId ||
                searchFilters.name.trim()) && (
                <div
                  style={{
                    textAlign: 'center',
                    padding: appTheme.spacing.md,
                    color: appTheme.colors.text.tertiary,
                    fontSize: appTheme.typography.fontSizes.sm,
                  }}
                >
                  No users found. Try adjusting your filters.
                </div>
              )}
          </ModalContent>
        </ModalOverlay>
      )}
    </CreateTaskContainer>
  );
}
