'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, Link, Send } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface AttachLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSendLink: (url: string) => void;
}

// Styled components
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: ${props => props.$isOpen ? 'fadeIn 0.2s ease-out' : 'none'};

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
  animation: slideIn 0.3s ease-out;

  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  
  &:hover {
    background-color: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
`;

const FormGroup = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
`;

const FormLabel = styled.label`
  display: block;
  margin-bottom: ${appTheme.spacing.sm};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const FormInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.md};
  border: 2px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.primary};
  background-color: ${appTheme.colors.background.main};
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  &::placeholder {
    color: ${appTheme.colors.text.light};
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: ${appTheme.typography.fontSizes.xs};
  margin-top: ${appTheme.spacing.xs};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  margin-top: ${appTheme.spacing.xl};
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.md} ${appTheme.spacing.xl};
  border: 2px solid transparent;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  min-width: 100px;
  justify-content: center;

  ${props =>
    props.$variant === 'primary'
      ? `
    background-color: ${appTheme.colors.primary};
    color: white;
    
    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.primaryHover};
      transform: translateY(-1px);
      box-shadow: ${appTheme.shadows.md};
    }
  `
      : `
    background-color: transparent;
    color: ${appTheme.colors.text.secondary};
    border-color: ${appTheme.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${appTheme.colors.background.light};
      color: ${appTheme.colors.text.primary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
`;

// URL validation function
const isValidUrl = (string: string): boolean => {
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
};

export default function AttachLinkModal({
  isOpen,
  onClose,
  onSendLink,
}: AttachLinkModalProps) {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setUrl('');
      setError('');
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedUrl = url.trim();
    
    if (!trimmedUrl) {
      setError('Please enter a URL');
      return;
    }

    if (!isValidUrl(trimmedUrl)) {
      setError('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    setError('');
    setIsSubmitting(true);

    try {
      await onSendLink(trimmedUrl);
      onClose();
    } catch (error) {
      console.error('Error sending link:', error);
      setError('Failed to send link. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
    if (error) {
      setError(''); // Clear error when user starts typing
    }
  };

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()} onKeyDown={handleKeyDown}>
        <ModalHeader>
          <ModalTitle>
            <Link size={20} />
            Attach Link
          </ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <form onSubmit={handleSubmit}>
            <FormGroup>
              <FormLabel htmlFor="url">URL</FormLabel>
              <FormInput
                id="url"
                type="url"
                value={url}
                onChange={handleInputChange}
                placeholder="https://example.com"
                autoFocus
                disabled={isSubmitting}
              />
              {error && <ErrorMessage>{error}</ErrorMessage>}
            </FormGroup>

            <ButtonGroup>
              <Button type="button" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" $variant="primary" disabled={isSubmitting || !url.trim()}>
                {isSubmitting ? (
                  'Sending...'
                ) : (
                  <>
                    <Send size={16} />
                    Send
                  </>
                )}
              </Button>
            </ButtonGroup>
          </form>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
}
